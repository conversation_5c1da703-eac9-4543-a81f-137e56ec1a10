import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../app.constants';
import { Domain, DOMAIN_TYPES, DomainRow, DomainType, STATIC_DOMAIN_TYPES, StaticDomainType } from '../../common/models/domain.model';

@Injectable()
export class EntityDomainService {
  private cachedItem: Record<string, Domain> = {};

  constructor(
    private readonly http: HttpClient,
    private readonly notifications: SwuiNotificationsService
  ) { }

  setEntityDomain(domainType: DomainType, domainId: string, path: string = ':', staticDomainType?: StaticDomainType) {
    return this.invoke('PUT', `${this.getUrl(path, domainType)}${domainId}`, domainType, staticDomainType);
  }

  getEntityDomain(domainType: DomainType, path: string = ':', force = false, staticDomainType?: StaticDomainType) {
    if (!force) {
      if (this.cachedItem[DOMAIN_TYPES[domainType]]) {
        return of(this.cachedItem[DOMAIN_TYPES[domainType]]);
      }
    }
    return this.invoke('GET', `${this.getUrl(path, domainType)}`, domainType, staticDomainType);
  }

  removeEntityDomain(domainType: DomainType, path: string = ':', staticDomainType?: StaticDomainType) {
    return this.invoke('DELETE', `${this.getUrl(path, domainType)}`, domainType, staticDomainType);
  }

  bulkOperation(bulkRequestData: any[]) {
    return this.http.post(`${API_ENDPOINT}/entities/bulk-operation`, bulkRequestData).pipe(
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  private invoke(method: string, url: string, domainType: DomainType, staticDomainType = STATIC_DOMAIN_TYPES.static) {
    const params = domainType === DOMAIN_TYPES.static ? { type: staticDomainType } : undefined;
    return this.http.request(method, url, { params }).pipe(
      map<DomainRow, Domain>((record) => {
        for (const property of ['createdAt', 'updatedAt']) {
          if (record[property] === ZERO_TIME) {
            record[property] = null;
          }
        }
        return {
          ...record,
          _meta: {
            createdAt: record.createdAt && moment(record.createdAt),
            updatedAt: record.updatedAt && moment(record.updatedAt),
          }
        };
      }),
      tap((data: Domain) => {
        this.cachedItem[DOMAIN_TYPES[domainType]] = data;
      }),
      catchError((error: HttpErrorResponse) => {
        this.notifications.error(error?.error?.message);
        return throwError(error);
      }),
    );
  }

  private getUrl(path: string, type: DomainType): string {
    return `${API_ENDPOINT}/${path !== ':' ? path : ''}/entitydomain/${type}`;
  }
}

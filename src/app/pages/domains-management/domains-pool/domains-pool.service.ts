import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { BehaviorSubject, Observable, of, Subject, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_ENDPOINT, ZERO_TIME } from '../../../app.constants';
import { Domain, DOMAIN_TYPES, DomainPool, DomainPoolRow, DomainType } from '../../../common/models/domain.model';

export function toDomainPool(record?: DomainPoolRow): DomainPool {
  if (!record) {
    return undefined;
  }
  for (const property of ['createdAt', 'updatedAt']) {
    if (record[property] === ZERO_TIME) {
      record[property] = null;
    }
  }
  return {
    ...record,
    _meta: {
      createdAt: record.createdAt && moment(record.createdAt),
      updatedAt: record.updatedAt && moment(record.updatedAt),
    }
  };
}

@Injectable()
export class DomainsPoolService {
  items = new BehaviorSubject<DomainPool[]>([]);
  isGridChanged$ = new Subject<boolean>();

  private cachedItems: DomainPool[];

  constructor(private readonly http: HttpClient, private readonly notifications: SwuiNotificationsService) {
  }

  getList(path?: string, force?: boolean): Observable<DomainPool[]> {
    if (!force) {
      if (this.cachedItems) {
        return of(this.cachedItems);
      }
    }
    return this.http.get<DomainPoolRow[]>(`${this.getUrl({path})}`)
      .pipe(
        map(response => response.map(toDomainPool)),
        tap(data => {
          this.cachedItems = data;
          this.items.next(data);
        }),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  delete(id: string): Observable<Object> {
    return this.http.delete(this.getUrl({id}))
      .pipe(
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  create(data: DomainPool): Observable<DomainPool> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.post<DomainPoolRow>(this.getUrl(), JSON.stringify(data))
      .pipe(
        map(toDomainPool),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  update(id: string, data: DomainPool): Observable<DomainPool> {
    if ('_meta' in data) {
      delete data['_meta'];
    }
    return this.http.patch<Domain>(this.getUrl({id}), JSON.stringify(data))
      .pipe(
        map(toDomainPool),
        catchError((error: HttpErrorResponse) => {
          this.notifications.error(error?.error?.message);
          return throwError(error);
        })
      );
  }

  private getUrl({type, id, path}: { path?: string, id?: string, type?: DomainType } = {}): string {
    return `${API_ENDPOINT}${path && path !== ':' ? `/entities/${path}` : ''}/domain-pools${id ? `/${id}` : ''}/${type || DOMAIN_TYPES.static}`;
  }
}

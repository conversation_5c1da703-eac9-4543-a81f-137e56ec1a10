import { TestBed } from '@angular/core/testing';
import { DomainConfigurationService } from './domain-configuration.service';
import { StaticDomainType, STATIC_DOMAIN_TYPES } from '../models/domain.model';

describe('DomainConfigurationService', () => {
  let service: DomainConfigurationService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(DomainConfigurationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getDomainTypeConfiguration', () => {
    it('should return configuration for static domain type', () => {
      const config = service.getDomainTypeConfiguration(STATIC_DOMAIN_TYPES.static);
      expect(config).toBeDefined();
      expect(config.type).toBe('static');
      expect(config.displayName).toBe('Static Domain');
      expect(config.enabled).toBe(true);
    });

    it('should return configuration for lobby domain type', () => {
      const config = service.getDomainTypeConfiguration(STATIC_DOMAIN_TYPES.lobby);
      expect(config).toBeDefined();
      expect(config.type).toBe('lobby');
      expect(config.displayName).toBe('Lobby Domain');
      expect(config.enabled).toBe(true);
    });

    it('should return configuration for live-streaming domain type', () => {
      const config = service.getDomainTypeConfiguration(STATIC_DOMAIN_TYPES['live-streaming']);
      expect(config).toBeDefined();
      expect(config.type).toBe('live-streaming');
      expect(config.displayName).toBe('Live Streaming Domain');
      expect(config.enabled).toBe(true);
    });

    it('should return configuration for ehub domain type', () => {
      const config = service.getDomainTypeConfiguration(STATIC_DOMAIN_TYPES.ehub);
      expect(config).toBeDefined();
      expect(config.type).toBe('ehub');
      expect(config.displayName).toBe('E-Hub Domain');
      expect(config.enabled).toBe(true);
    });
  });

  describe('getDomainTypeDefaultSettings', () => {
    it('should return default settings for static domain', () => {
      const settings = service.getDomainTypeDefaultSettings(STATIC_DOMAIN_TYPES.static);
      expect(settings).toBeDefined();
      expect(settings.defaultStatus).toBe('active');
      expect(settings.requireSSL).toBe(true);
      expect(settings.defaultPort).toBe(443);
    });

    it('should return default settings for lobby domain', () => {
      const settings = service.getDomainTypeDefaultSettings(STATIC_DOMAIN_TYPES.lobby);
      expect(settings).toBeDefined();
      expect(settings.defaultStatus).toBe('active');
      expect(settings.requireSSL).toBe(true);
      expect(settings.additionalConfig?.gameListEnabled).toBe(true);
      expect(settings.additionalConfig?.userProfileEnabled).toBe(true);
    });

    it('should return default settings for live-streaming domain', () => {
      const settings = service.getDomainTypeDefaultSettings(STATIC_DOMAIN_TYPES['live-streaming']);
      expect(settings).toBeDefined();
      expect(settings.defaultStatus).toBe('active');
      expect(settings.requireSSL).toBe(true);
      expect(settings.additionalConfig?.streamingEnabled).toBe(true);
      expect(settings.additionalConfig?.lowLatencyMode).toBe(true);
    });

    it('should return default settings for ehub domain', () => {
      const settings = service.getDomainTypeDefaultSettings(STATIC_DOMAIN_TYPES.ehub);
      expect(settings).toBeDefined();
      expect(settings.defaultStatus).toBe('active');
      expect(settings.requireSSL).toBe(true);
      expect(settings.additionalConfig?.apiEnabled).toBe(true);
      expect(settings.additionalConfig?.webhooksEnabled).toBe(true);
    });
  });

  describe('getEnabledDomainTypes', () => {
    it('should return all enabled domain types', () => {
      const enabledTypes = service.getEnabledDomainTypes();
      expect(enabledTypes).toContain('static');
      expect(enabledTypes).toContain('lobby');
      expect(enabledTypes).toContain('live-streaming');
      expect(enabledTypes).toContain('ehub');
      expect(enabledTypes.length).toBe(4);
    });
  });

  describe('isDomainTypeEnabled', () => {
    it('should return true for enabled domain types', () => {
      expect(service.isDomainTypeEnabled(STATIC_DOMAIN_TYPES.static)).toBe(true);
      expect(service.isDomainTypeEnabled(STATIC_DOMAIN_TYPES.lobby)).toBe(true);
      expect(service.isDomainTypeEnabled(STATIC_DOMAIN_TYPES['live-streaming'])).toBe(true);
      expect(service.isDomainTypeEnabled(STATIC_DOMAIN_TYPES.ehub)).toBe(true);
    });
  });

  describe('validateDomainTypeConfiguration', () => {
    it('should validate correct configuration', () => {
      const validSettings = {
        defaultStatus: 'active' as const,
        requireSSL: true,
        defaultPort: 443
      };
      expect(service.validateDomainTypeConfiguration(STATIC_DOMAIN_TYPES.static, validSettings)).toBe(true);
    });

    it('should reject invalid status', () => {
      const invalidSettings = {
        defaultStatus: 'invalid' as any,
        requireSSL: true
      };
      expect(service.validateDomainTypeConfiguration(STATIC_DOMAIN_TYPES.static, invalidSettings)).toBe(false);
    });

    it('should reject invalid port', () => {
      const invalidSettings = {
        defaultStatus: 'active' as const,
        requireSSL: true,
        defaultPort: 70000
      };
      expect(service.validateDomainTypeConfiguration(STATIC_DOMAIN_TYPES.static, invalidSettings)).toBe(false);
    });
  });

  describe('mergeWithDefaults', () => {
    it('should merge custom settings with defaults', () => {
      const customSettings = {
        defaultDescription: 'Custom description',
        additionalConfig: {
          customProperty: true
        }
      };
      
      const merged = service.mergeWithDefaults(STATIC_DOMAIN_TYPES.static, customSettings);
      
      expect(merged.defaultDescription).toBe('Custom description');
      expect(merged.defaultStatus).toBe('active'); // from defaults
      expect(merged.requireSSL).toBe(true); // from defaults
      expect(merged.additionalConfig?.customProperty).toBe(true);
      expect(merged.additionalConfig?.cacheEnabled).toBe(true); // from defaults
    });
  });
});

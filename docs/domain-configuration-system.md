# Domain Configuration System

## Overview

The Domain Configuration System provides comprehensive support for all static domain types in the entity system. It includes default configurations, validation, and management capabilities for different domain types including Static, Lobby, Live Streaming, and E-Hub domains.

## Supported Domain Types

### 1. Static Domain (`static`)
- **Purpose**: Standard static domain for general website content
- **Default Settings**:
  - Status: Active
  - SSL Required: Yes
  - Default Port: 443
  - Cache Enabled: Yes
  - Compression Enabled: Yes

### 2. Lobby Domain (`lobby`)
- **Purpose**: Domain specifically configured for lobby functionality
- **Default Settings**:
  - Status: Active
  - SSL Required: Yes
  - Default Port: 443
  - Game List Enabled: Yes
  - User Profile Enabled: Yes
  - Chat Enabled: Yes
  - Promotions Enabled: Yes

### 3. Live Streaming Domain (`live-streaming`)
- **Purpose**: Domain optimized for live streaming content
- **Default Settings**:
  - Status: Active
  - SSL Required: Yes
  - Default Port: 443
  - Streaming Enabled: Yes
  - Low Latency Mode: Yes
  - Adaptive Bitrate: Yes
  - Recording Enabled: No

### 4. E-Hub Domain (`ehub`)
- **Purpose**: Domain for e-hub integration and services
- **Default Settings**:
  - Status: Active
  - SSL Required: Yes
  - Default Port: 443
  - API Enabled: Yes
  - Webhooks Enabled: Yes
  - Analytics Enabled: Yes
  - Integration Mode: Full

## Architecture

### Core Components

1. **Domain Models** (`src/app/common/models/domain.model.ts`)
   - `DomainTypeConfiguration`: Interface for domain type configurations
   - `DomainTypeSettings`: Interface for domain type specific settings
   - `DOMAIN_TYPE_CONFIGURATIONS`: Default configurations for all domain types

2. **Domain Configuration Service** (`src/app/common/services/domain-configuration.service.ts`)
   - Manages domain type configurations
   - Provides default settings
   - Validates configurations
   - Merges custom settings with defaults

3. **Domain Validation Service** (`src/app/common/services/domain-validation.service.ts`)
   - Validates domain names
   - Validates domain type selections
   - Validates domain configurations
   - Provides validation error messages

4. **Entity Domain Service** (`src/app/pages/domains-management/entity-domain.service.ts`)
   - Extended to support domain type configurations
   - Applies defaults when creating/retrieving domains
   - Handles domain type specific operations

## Usage

### Getting Domain Type Configuration

```typescript
import { DomainConfigurationService } from '../services/domain-configuration.service';

// Get configuration for a specific domain type
const lobbyConfig = this.domainConfigService.getDomainTypeConfiguration('lobby');

// Get default settings for a domain type
const defaultSettings = this.domainConfigService.getDomainTypeDefaultSettings('lobby');

// Check if domain type is enabled
const isEnabled = this.domainConfigService.isDomainTypeEnabled('lobby');
```

### Creating Domains with Defaults

```typescript
import { EntityDomainService } from '../entity-domain.service';

// Create domain with default configuration
const domainData = {
  domain: 'lobby.example.com',
  description: 'Custom lobby domain'
};

const domainWithDefaults = this.entityDomainService.createDomainWithDefaults(
  domainData, 
  'lobby'
);
```

### Validation

```typescript
import { DomainValidationService } from '../services/domain-validation.service';

// Validate domain name
const domainNameValidator = this.domainValidationService.domainNameValidator();

// Validate domain type
const domainTypeValidator = this.domainValidationService.domainTypeValidator();

// Validate complete domain
const validationErrors = this.domainValidationService.validateDomain(domain);
```

## Configuration Structure

### DomainTypeConfiguration Interface

```typescript
interface DomainTypeConfiguration {
  type: StaticDomainType;
  displayName: string;
  defaultSettings: DomainTypeSettings;
  enabled: boolean;
  description?: string;
}
```

### DomainTypeSettings Interface

```typescript
interface DomainTypeSettings {
  defaultStatus: 'active' | 'suspended';
  defaultDescription?: string;
  requireSSL: boolean;
  defaultPort?: number;
  additionalConfig?: { [key: string]: any };
}
```

## Validation Rules

### Domain Name Validation
- Must follow standard domain name format
- Cannot be reserved domains (localhost, example.com, test.com)
- Required field

### Domain Type Validation
- Must be one of the supported static domain types
- Must be enabled in configuration
- Required field

### Configuration Validation
- Status must be 'active' or 'suspended'
- SSL setting must be boolean
- Port must be between 1 and 65535
- Domain type specific validations apply

## Extending the System

### Adding New Domain Types

1. Update `StaticDomainType` in `domain.model.ts`
2. Add configuration to `DOMAIN_TYPE_CONFIGURATIONS`
3. Update validation rules in `DomainValidationService`
4. Add tests for the new domain type

### Customizing Default Settings

Modify the `DOMAIN_TYPE_CONFIGURATIONS` object in `domain.model.ts` to change default settings for any domain type.

## Testing

The system includes comprehensive tests:
- `domain-configuration.service.spec.ts`: Tests for configuration service
- `domain-validation.service.spec.ts`: Tests for validation service

Run tests with:
```bash
ng test
```

## Migration

Existing domains will automatically receive default configurations based on their type when accessed through the updated services. No manual migration is required.
